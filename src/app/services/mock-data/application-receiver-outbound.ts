export const MockApplicationReceiverOutbound: string[] = [
  'CSXI',
  'SBD',
  'EXPRAIL',
  'CSXIN<PERSON>',
  'TDSIRAMP',
  'J26EC',
  '59-2869009',
  'CSX1',
  'CN',
  'CFE',
  'MH<PERSON>',
  'NCIR',
  'POPBALCSX',
  '36964',
  '<PERSON>X<PERSON>',
  'POHC',
  'CSXT',
  '36460',
  'MQT',
  'UP',
  'MNBR',
  '6125A8A',
  '007941321',
  'CSXTPROD',
  'TL20',
  'CSXTITBSYSTEM',
  'CPRST',
  'ST',
  'FRR',
  'SUBARU',
  'BNSF',
  'BPIE',
  'RRWS',
  'RRWE',
  'RAILINC',
  'SBDCSXT',
  'TRANSFLO',
  'E4LSW',
  'PAS',
  'SHIPCSX',
  'INRD',
  'TSM',
  'H9T4A',
  'CSO',
  'AGR',
  'CPRSP',
  'ISSV',
  '&CSM',
  '&MRK',
  'RLX0216',
  'CSLI',
  'CSXTCR<PERSON>',
  'MON<PERSON>NT<PERSON>',
  'GITM',
  'WE',
  'USCT',
  '592869009',
  'CSXIHOST',
  'CSXCLP',
  '29749',
  'GRAINCRAFT',
  'TRNHLI',
  'CSXTCSLI',
  'BOCT',
  'NS',
  'SAPT',
  'FGLK',
  'RZ1UMXU',
  'STCS',
  'CASS151700T',
  '19-4282745',
  'USCP',
  'FEC',
  'SCXF',
  'BB',
  'BAIL',
  'GMPTSTECH',
  'NPB',
  'ITELIPG',
  'ISSP',
  'TFKB',
  'ESPN',
  'INOS',
  'CSXR',
  'VWASN',
  'GEAP',
  '06906430',
  'CSX',
  '065906430',
  'CS',
  'CPRS',
  'NUCB',
  '208334425',
  '65906430',
  'CSXO',
  'CSXUMAX',
];
