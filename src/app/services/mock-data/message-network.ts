export const MockMessageNetwork: string[] = [
  'TRANSENTRIC',
  'NS CRISP NETWORK',
  'TTX',
  'SIMSTESTMESSAGING',
  'SMS APPLICATION QUEUE',
  'IPRO.GRE.XML.OUTBOUND',
  'IPRO.LOU.XML.OUTBOUND',
  'IPRO.NBE.XML.OUTBOUND',
  'WABTECH Outbound',
  'IPRO.SAV.XML.OUTBOUND',
  'APLXPOLOGISTICS',
  'EDIINB_UPLOAD',
  'AWSBOLTESTMESSAGING',
  'AUTOEDIGMMSGS',
  'IPRO.JAX.XML.OUTBOUND',
  'IPRO.CNC.XML.OUTBOUND',
  'IPRO.CIN.XML.OUTBOUND',
  'IPRO.CHS.XML.OUTBOUND',
  'RAILINC',
  'TRASHCAN',
  'CSXAPPLICATIONS',
  'IPRO.BED.XML.OUTBOUND',
  'CSXBOLSYSTEM',
  'IPRO.MEM.XML.OUTBOUND',
  'IPRO.KRN.XML.OUTBOUND',
  'IPRO.CHA.XML.OUTBOUND',
  'TRANSCORE',
  'RETRANSMITS',
  'Conrail Railroad',
  'SHIPCSXBOLS',
  'HUBGROUP',
  'IPRO.IND.XML.OUTBOUND',
  'IPRO.POR.XML.OUTBOUND',
  'IPRO.BAL.XML.OUTBOUND',
  'CSXSAPApplication',
  'CSXGISB2BTOOL',
  'KLEINSCHIMDT',
  'SIMSTESTPROCESSING',
  'CSXAPPLICATIONSOB',
  'AUTOEDIBOLS',
  'CSX INTERNAL APPS',
  'IPRO.FAB.XML.OUTBOUND',
  'MQMon_Dev\\mqmonntp.exe',
  'IPRO.TAM.XML.OUTBOUND',
  'IPRO.CLE.XML.OUTBOUND',
  'IPRO.STL.XML.OUTBOUND',
  'IPRO.CAI.XML.OUTBOUND',
  'AUTOEDI417TOASN',
  'IRDE',
  'CSXAPPLICATION',
  'Bill_of_lading',
  'sageInbOutRouterBusine',
  'TRADELENS',
  'AUTOEDISPOTEVENTS',
  'Admin_Dev\\mqmonntp.exe',
  'IPRO.FIV.XML.OUTBOUND',
  'IPRO.XML.OUTBOUND',
  'CC',
  'B2BTOOLEDI feed',
  'PEGASUS 404 BILLLINGS',
  'IPRO.BUF.XML.OUTBOUND',
  'IPRO.DET.XML.OUTBOUND',
  'IPRO.NSH.XML.OUTBOUND',
  'IPRO.SYR.XML.OUTBOUND',
];
