export const MockMessageStatus: {
  messageStatusC: string;
  messageStatusX: string;
  effectiveDate: string;
  expirationDate: string;
}[] = [
  {
    messageStatusC: 'IN_EDIACPT',
    messageStatusX: 'Accepted EDI Transmission',
    effectiveDate: '2014-12-08T12:46:24.000+00:00',
    expirationDate: '2019-01-16T00:00:00.000+00:00',
  },
  {
    messageStatusC: 'IN_EDIWARN',
    messageStatusX: 'Accepted EDI with Warning',
    effectiveDate: '2014-12-08T12:46:24.000+00:00',
    expirationDate: '2019-01-16T00:00:00.000+00:00',
  },
  {
    messageStatusC: 'IN824_REJECTED',
    messageStatusX: 'Acknowledgement 824 Error/Rejected Status',
    effectiveDate: '2020-04-06T21:02:01.000+00:00',
    expirationDate: '4758-03-03T21:02:01.000+00:00',
  },
  {
    messageStatusC: 'IN824_WARNINGS',
    messageStatusX: 'Acknowledgement 824 Warning',
    effectiveDate: '2020-04-06T21:02:01.000+00:00',
    expirationDate: '4758-03-03T21:02:01.000+00:00',
  },
  {
    messageStatusC: 'IN824_UNKNOWN',
    messageStatusX: 'Acknowledgement 824 has UNKNOWN status',
    effectiveDate: '2020-04-06T21:02:01.000+00:00',
    expirationDate: '4758-03-03T21:02:01.000+00:00',
  },
  {
    messageStatusC: 'IN824_ACCEPTED',
    messageStatusX: 'Acknowledgment 824 Accepted Status',
    effectiveDate: '2020-04-06T21:01:43.000+00:00',
    expirationDate: '4758-03-03T21:01:43.000+00:00',
  },
  {
    messageStatusC: 'OUT_APERR',
    messageStatusX: 'Application Critical Error',
    effectiveDate: '2014-12-08T12:46:29.000+00:00',
    expirationDate: '2019-01-16T00:00:00.000+00:00',
  },
  {
    messageStatusC: 'OUT_APRECV',
    messageStatusX: 'Application Message Received',
    effectiveDate: '2014-12-08T12:27:05.000+00:00',
    expirationDate: '2028-08-16T00:00:00.000+00:00',
  },
  {
    messageStatusC: 'OUT824_ACCEPTED',
    messageStatusX: 'CSX App Response 824 Accepted Status',
    effectiveDate: '2020-04-06T21:02:01.000+00:00',
    expirationDate: '4758-03-03T21:02:01.000+00:00',
  },
  {
    messageStatusC: 'OUT824_REJECTED',
    messageStatusX: 'CSX App Response 824 Error/Rejected',
    effectiveDate: '2020-04-06T21:02:01.000+00:00',
    expirationDate: '4758-03-03T21:02:01.000+00:00',
  },
  {
    messageStatusC: 'OUT824_WARNINGS',
    messageStatusX: 'CSX App Response 824 Warning',
    effectiveDate: '2020-04-06T21:02:01.000+00:00',
    expirationDate: '4758-03-03T21:02:01.000+00:00',
  },
  {
    messageStatusC: 'OUT824_UNKNOWN',
    messageStatusX: 'CSX App Response 824 has UNKNOWN status',
    effectiveDate: '2020-04-06T21:02:01.000+00:00',
    expirationDate: '4758-03-03T21:02:01.000+00:00',
  },
  {
    messageStatusC: 'OUT_EDIACPT',
    messageStatusX: 'EDI Acknowledged Accepted',
    effectiveDate: '2014-12-08T12:46:29.000+00:00',
    expirationDate: '2019-01-16T00:00:00.000+00:00',
  },
  {
    messageStatusC: 'OUT_ACPTPART',
    messageStatusX: 'EDI Acknowledged Partial',
    effectiveDate: '2014-12-08T12:46:29.000+00:00',
    expirationDate: '2019-01-16T00:00:00.000+00:00',
  },
  {
    messageStatusC: 'OUT_EDIRJCT',
    messageStatusX: 'EDI Acknowledged Rejected',
    effectiveDate: '2014-12-08T12:46:29.000+00:00',
    expirationDate: '2019-01-16T00:00:00.000+00:00',
  },
  {
    messageStatusC: 'OUT_EDIWARN',
    messageStatusX: 'EDI Acknowledged Warning',
    effectiveDate: '2014-12-08T12:46:29.000+00:00',
    expirationDate: '2019-01-16T00:00:00.000+00:00',
  },
  {
    messageStatusC: 'IN_CUSTERR',
    messageStatusX: 'EDI Customer Map Err Warning',
    effectiveDate: '2014-12-08T12:46:24.000+00:00',
    expirationDate: '2019-01-16T00:00:00.000+00:00',
  },
  {
    messageStatusC: 'OUT_CUSTERR',
    messageStatusX: 'EDI Customer Map Err Warning',
    effectiveDate: '2014-12-08T12:46:29.000+00:00',
    expirationDate: '2019-01-16T00:00:00.000+00:00',
  },
  {
    messageStatusC: 'IN_EDIREC',
    messageStatusX: 'EDI Received',
    effectiveDate: '2014-12-08T12:46:24.000+00:00',
    expirationDate: '2019-01-16T00:00:00.000+00:00',
  },
  {
    messageStatusC: 'OUT_EDISEND',
    messageStatusX: 'EDI Transmitted',
    effectiveDate: '2014-12-08T12:46:29.000+00:00',
    expirationDate: '2019-01-16T00:00:00.000+00:00',
  },
  {
    messageStatusC: 'IN_PROCBYP',
    messageStatusX: 'Inbound EDI Bypassed Processing per Setup',
    effectiveDate: '2015-03-15T19:31:17.000+00:00',
    expirationDate: '2019-01-16T00:00:00.000+00:00',
  },
  {
    messageStatusC: 'OUT_APCT',
    messageStatusX: 'Outbound Application Acknowledged Accepted',
    effectiveDate: '2014-12-08T12:46:29.000+00:00',
    expirationDate: '2019-01-16T00:00:00.000+00:00',
  },
  {
    messageStatusC: 'OUT_RJCT',
    messageStatusX: 'Outbound Application Acknowledged Rejected',
    effectiveDate: '2014-12-08T12:46:29.000+00:00',
    expirationDate: '2019-01-16T00:00:00.000+00:00',
  },
  {
    messageStatusC: 'OUT_APLWAR',
    messageStatusX: 'Outbound Application Acknowledged Warning',
    effectiveDate: '2014-12-08T12:46:01.000+00:00',
    expirationDate: '2019-01-16T00:00:00.000+00:00',
  },
  {
    messageStatusC: 'IN_ACPTPART',
    messageStatusX: 'Partially Rejected EDI Transmitted',
    effectiveDate: '2014-12-08T12:46:24.000+00:00',
    expirationDate: '2019-01-16T00:00:00.000+00:00',
  },
  {
    messageStatusC: 'IN_PNDTRN',
    messageStatusX: 'Pending Translation',
    effectiveDate: '2014-12-08T12:46:24.000+00:00',
    expirationDate: '2019-01-16T00:00:00.000+00:00',
  },
  {
    messageStatusC: 'IN_EDIRJCT',
    messageStatusX: 'Rejected EDI Transmission',
    effectiveDate: '2014-12-08T12:46:24.000+00:00',
    expirationDate: '2019-01-16T00:00:00.000+00:00',
  },
  {
    messageStatusC: 'IN_EDIRTRNS',
    messageStatusX: 'Retransmitted',
    effectiveDate: '2014-12-08T12:46:24.000+00:00',
    expirationDate: '2019-01-16T00:00:00.000+00:00',
  },
  {
    messageStatusC: 'OUT_EDIRTRNS',
    messageStatusX: 'Retransmitted',
    effectiveDate: '2014-12-08T12:46:29.000+00:00',
    expirationDate: '2019-01-16T00:00:00.000+00:00',
  },
  {
    messageStatusC: 'OUT_NTWKERRS',
    messageStatusX: 'Routing Errors Network',
    effectiveDate: '2014-12-08T12:46:29.000+00:00',
    expirationDate: '2019-01-16T00:00:00.000+00:00',
  },
  {
    messageStatusC: 'IN_X12TERR',
    messageStatusX: 'Translation Error',
    effectiveDate: '2014-12-08T12:46:24.000+00:00',
    expirationDate: '2019-01-16T00:00:00.000+00:00',
  },
];
