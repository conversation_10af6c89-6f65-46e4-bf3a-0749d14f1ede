import { Injectable } from '@angular/core';
import { ToastMessageOptions } from 'primeng/api';
import { Observable, of, throwError } from 'rxjs';
import { SuccessMessageService } from './api-result-messages.service'; // Assuming this is the SuccessMessageService

@Injectable({
  providedIn: 'root',
})
export class MessageActionHandlerService {
  constructor(private successMessageService: SuccessMessageService) {}

  executeAction(
    action: string,
    messageIds: string[],
    params: { email?: string } = {},
  ): Observable<ToastMessageOptions> {
    // Validation: Check if messageIds are provided
    if (!messageIds || messageIds.length === 0) {
      return throwError(() => new Error('No messages selected'));
    }

    // Define valid action types
    const validActions = [
      'download',
      'email',
      'retransmit',
      'specialRetransmit',
      'unassign',
      'route',
    ] as const;
    type ValidAction = (typeof validActions)[number];

    // Validate action
    if (!validActions.includes(action as ValidAction)) {
      return throwError(() => new Error(`Unknown action: ${action}`));
    }

    // Get success message from SuccessMessageService
    const successMessage = this.successMessageService.getSuccessMessage(action as ValidAction, {
      count: messageIds.length,
      email: params.email,
    });

    return of({
      summary: successMessage.summary,
      detail: successMessage.details,
      messageIds: messageIds,
      action: action,
    });
  }
}
