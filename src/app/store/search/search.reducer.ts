import { createReducer, on } from '@ngrx/store';

import * as SearchActions from './search.actions';

// Search state model
export interface SearchStateModel {
  formValues: {
    searchType: string;
    direction: string;
    startDate: Date;
    endDate: Date;
    transactionView: string;
    equipmentSearchType: string;
    equipmentIds: string;
    transactionAttributes: string;
    inboundFields: Array<{
      label: string;
      placeholder: string;
      value: string;
      key: string;
    }>;
    outboundFields: Array<{
      label: string;
      placeholder: string;
      value: string;
      key: string;
    }>;
    // Sidebar filter checkboxes
    sidebarInboundFilters?: {
      isaReceiver: boolean;
      gsReceiver: boolean;
    };
    sidebarOutboundFilters?: {
      isaSender: boolean;
      gsSender: boolean;
    };
    sidebarEdiAttributesFilters?: {
      messageId: boolean;
      network: boolean;
      messageStatus: boolean;
      controlNumber: boolean;
    };
  };
  lastSearch: Record<string, string>;
  hasSearched: boolean;
}

// Default state
export const initialState: SearchStateModel = {
  formValues: {
    searchType: 'edi',
    direction: 'inbound-outbound',
    startDate: new Date(new Date().setDate(new Date().getDate() - 15)),
    endDate: new Date(),
    transactionView: 'all',
    equipmentSearchType: 'equipment',
    equipmentIds: '',
    transactionAttributes: '',
    inboundFields: [
      { label: 'ISA Sender', placeholder: 'ISA Sender', value: '', key: 'isaSender' },
      { label: 'GS Sender', placeholder: 'GS Sender', value: '', key: 'gsSender' },
    ],
    outboundFields: [
      { label: 'ISA Receiver', placeholder: 'ISA Receiver', value: '', key: 'isaReceiver' },
      { label: 'GS Receiver', placeholder: 'GS Receiver', value: '', key: 'gsReceiver' },
    ],
    // Initialize sidebar filter checkboxes with default values (all unchecked)
    sidebarInboundFilters: {
      isaReceiver: false,
      gsReceiver: false,
    },
    sidebarOutboundFilters: {
      isaSender: false,
      gsSender: false,
    },
    sidebarEdiAttributesFilters: {
      messageId: false,
      network: false,
      messageStatus: false,
      controlNumber: false,
    },
  },
  lastSearch: {},
  hasSearched: false,
};

export const searchReducer = createReducer(
  initialState,
  on(SearchActions.saveFormValues, (state, { payload }) => ({
    ...state,
    formValues: payload,
  })),
  on(SearchActions.saveSearchParams, (state, { payload }) => ({
    ...state,
    lastSearch: payload,
    hasSearched: true,
  })),
  on(SearchActions.resetState, () => initialState),
);
