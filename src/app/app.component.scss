:host {
  display: block;
  height: 100%;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 60px;
  padding: 0 1rem;
  border-bottom: 1px solid #ddd;
  box-shadow:
    0px 1px 3px 0px #00001f,
    0px 1px 1px 0px #000024,
    0px 2px 1px -1px #000033;

  h1,
  p-selectbutton,
  .action-links {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  h1 {
    justify-content: flex-start;
    margin: 0;
  }

  .action-links {
    justify-content: flex-end;
    gap: 10px;
  }
}

.page-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.page-content {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  overflow: scroll;
}
