<div class="page-container">
  <csx-header
    [pageTitle]="'DXH'"
    [pageSubtitle]="'Digital Exchange Hub'"
    [supportButtonText]="'Help'"
    [avatarLetter]="'U'"
    [searchPlaceholder]="'Search Here'"
  ></csx-header>
  <div class="page-header">
    <h1 class="csx-text-headline">{{ breadcrumbLabel }}</h1>
    <div class="action-links">
      <p-button label="Upload Request" icon="pi pi-upload" [link]="true" variant="text" />
      <p-button label="Download Request" icon="pi pi-download" [link]="true" variant="text" />
      <p-button label="Add Customer" icon="pi pi-plus" [link]="true" variant="text" />
    </div>
  </div>
  <p-breadcrumb *ngIf="!hideBreadcrumb" class="max-w-full" [model]="items" [home]="home" />
  <div class="page-content">
    <router-outlet></router-outlet>
  </div>
</div>
