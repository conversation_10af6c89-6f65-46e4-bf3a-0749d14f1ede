<div class="search-results-container">
  <div *ngIf="!messages || !messages.length" class="no-results">
    <div class="no-results-layout">
      <img src="assets/images/no-results.svg" alt="No Results" />
      <h2 class="csx-text-title-md">We found 0 results for your criteria</h2>
      <ul>
        <li>Check the search criteria</li>
        <li>Use different search criteria</li>
      </ul>
    </div>
  </div>

  <div *ngIf="messages && messages.length">
    <app-search-minibar />

    <p-contextmenu #cm [model]="contextMenuItems" />

    <p-table
      #dt
      stripedRows
      [value]="messages"
      [paginator]="true"
      [rowHover]="true"
      [rows]="10"
      [(selection)]="selectedMessages"
      [(contextMenuSelection)]="selectedContextMessage"
      [contextMenu]="cm"
      dataKey="id"
      stateStorage="session"
      stateKey="csx-dxh-search-results"
      [showCurrentPageReport]="true"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} most recent"
      [rowsPerPageOptions]="[10, 20, 50]"
      [globalFilterFields]="globalFilterFields"
      styleClass="p-datatable-striped"
    >
      <ng-template pTemplate="caption">
        <div class="global-table-actions">
          <div class="flex-outside"></div>
          <div class="">
            <p-selectbutton
              [options]="stateOptions"
              [(ngModel)]="inboundOutboundSelectedState"
              [multiple]="false"
              [style]="{ width: '200px' }"
            ></p-selectbutton>
          </div>
          <div class="table-search-row flex-outside">
            <p-select
              [options]="[
                { label: 'ISA Ctrl Number', value: 'interchangeControlNumber' },
                { label: 'GS Ctrl Number', value: 'gsControlNumber' },
              ]"
              [(ngModel)]="selectedCtrlNmbrField"
              (ngModelChange)="onFilterFieldChange()"
              styleClass="custom-dropdown ctrl-number"
            ></p-select>
            <p-iconfield>
              <p-inputicon styleClass="pi pi-search" />
              <input
                pInputText
                type="text"
                (input)="dt.filterGlobal($any($event.target).value, 'contains')"
                placeholder="Search all columns"
              />
            </p-iconfield>
          </div>
        </div>
      </ng-template>
      <ng-template pTemplate="header">
        <tr>
          <th style="width: 20px"></th>
          <th
            *ngFor="let col of columns"
            [pSortableColumn]="col.sortable ? col.field : null"
            [style]="getColumnStyle(col)"
          >
            {{ col.header }}
            <p-sortIcon *ngIf="col.sortable" [field]="col.field" />
          </th>
          <th style="width: 20px"></th>
          <th style="width: 20px"></th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-message>
        <tr [pContextMenuRow]="message">
          <td>
            <p-tableCheckbox [value]="message" />
          </td>
          <td *ngFor="let col of columns">
            <ng-container [ngSwitch]="col.field">
              <ng-container *ngSwitchCase="'interchangeSenderId'">
                <p-button
                  type="button"
                  iconPos="right"
                  [label]="message.interchangeSenderId"
                  [link]="true"
                  (onClick)="logIt('onClick')"
                  (onFocus)="logIt('onFocus')"
                  (onBlur)="logIt('onBlur')"
                  icon="pi pi-window-maximize"
                />
              </ng-container>
              <ng-container *ngSwitchCase="'interchangeControlNumber'">
                {{ message.interchangeControlNumber }}
              </ng-container>
              <ng-container *ngSwitchCase="'gsControlNumber'">
                {{ message.gsControlNumber }}
              </ng-container>
              <ng-container *ngSwitchCase="'id'">
                {{ message.id }}
              </ng-container>
              <ng-container *ngSwitchCase="'transactionSet'">
                <div class="related-messages-wrapper">
                  <div class="related-messages">
                    <div
                      *ngFor="let related of message.transactionSet"
                      class="related-message"
                      (click)="showRelatedModal(message, related)"
                    >
                      <div *ngIf="related.name !== 'App. Record'">
                        <p-badge [severity]="related.severity || 'success'" />
                        <a class="related-message-link">
                          {{ related.transactionSet }}
                          {{ related.status }}
                          {{ related.messageDateTime }}
                          {{ related.name }}
                          {{ related.description }}
                        </a>
                      </div>
                      <div *ngIf="related.name === 'App. Record'">
                        <i class="pi pi-arrow-right"></i>
                        <div class="app-record-cell" (click)="showRelatedModal(message, related)">
                          <p-badge [severity]="getAppRecordSeverity(message)" /><a
                            class="related-message-link"
                            >App. Record</a
                          >
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ng-container>
              <ng-container *ngSwitchCase="'applicationStatus'">
                <p-tag
                  *ngIf="message.applicationStatus"
                  [value]="message.applicationStatus"
                  severity="secondary"
                  styleClass="p-column-filter"
                />
              </ng-container>
              <ng-container *ngSwitchCase="'messageNetwork'">
                {{ message.messageNetwork }}
              </ng-container>
              <ng-container *ngSwitchCase="'messageDateTime'">
                {{ message.messageDateTime }}
              </ng-container>
            </ng-container>
          </td>
          <td>
            <i pTooltip="Unassigned" tooltipPosition="left" class="pi pi-exclamation-triangle"></i>
          </td>
          <td><i class="pi pi-history"></i></td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="100" height="300">No messages found.</td>
        </tr>
      </ng-template>
    </p-table>

    <app-actions-toolbar
      *ngIf="selectedMessages?.length"
      [selectable]="true"
      [selectedMessages]="selectedMessages"
      (actionTriggered)="onToolbarAction($event)"
    ></app-actions-toolbar>
  </div>

  <p-toast></p-toast>
</div>
