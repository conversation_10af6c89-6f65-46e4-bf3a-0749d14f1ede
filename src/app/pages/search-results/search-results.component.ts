import { CommonModule, Location } from '@angular/common';
import { Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Store } from '@ngrx/store';
import { Subscription, catchError, finalize, of } from 'rxjs';
import { BadgeModule } from 'primeng/badge';
import { ButtonModule } from 'primeng/button';
import { ContextMenuModule } from 'primeng/contextmenu';
import { DatePickerModule } from 'primeng/datepicker';
import { DialogModule } from 'primeng/dialog';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { InputTextModule } from 'primeng/inputtext';
import { MenuItem, MessageService } from 'primeng/api';
import { MultiSelectModule } from 'primeng/multiselect';
import { SelectButtonModule } from 'primeng/selectbutton';
import { SelectModule } from 'primeng/select';
import { Table, TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';
import { ToastModule } from 'primeng/toast';
import { TooltipModule } from 'primeng/tooltip';
import { ActionsToolbarComponent } from '../../components/actions-buttonbar/actions-toolbar.component';
import { SearchMinibarComponent } from 'app/components/search-minibar/search-minibar.component';
import { DialogRelatedComponent } from './dialog-related/dialog-related.component';
import * as SearchActions from 'app/store/search/search.actions';
import { DropdownOption, Message, MessageDirections, Transaction } from 'app/types';
import { EdiMessageService } from 'app/services/edi-message/edi-message';
import { FilterService } from 'primeng/api';
import { MessageActionHandlerService } from 'app/services/search-results/message-action-handler.service';
import { SHARED_ACTIONS } from 'app/services/search-results/shared-actions';
import { SearchParams, SearchService } from 'app/services/search/search.service';

@Component({
  selector: 'csx-search-results',
  standalone: true,
  providers: [DialogService, MessageService, MessageActionHandlerService],
  imports: [
    CommonModule,
    FormsModule,
    TableModule,
    InputTextModule,
    DatePickerModule,
    MultiSelectModule,
    TagModule,
    ButtonModule,
    BadgeModule,
    TooltipModule,
    DialogModule,
    ToastModule,
    SearchMinibarComponent,
    SelectButtonModule,
    ContextMenuModule,
    ActionsToolbarComponent,
    IconFieldModule,
    InputIconModule,
    SelectModule,
  ],
  templateUrl: './search-results.component.html',
  styleUrls: ['./search-results.component.scss'],
})
export class SearchResultsComponent implements OnInit, OnDestroy {
  @ViewChild('dt') table!: Table;

  // State properties
  selectedCtrlNmbrField = 'interchangeControlNumber';
  messageDetailId: string | null = null;
  messageDetails: Message | null = null;
  searchParams!: SearchParams;
  messages: Message[] = [];
  selectedMessages: Message[] = [];
  selectedContextMessage: Message | null = null;
  dateFilterValue: Date | null = null;
  dialogRef: DynamicDialogRef | null = null;
  isLoading = false;
  error: string | null = null;
  totalCount = 0;
  currentPage = 1;
  pageSize = 20;
  isLastPage = false;
  networkOptions: DropdownOption[] = [];

  // Dynamic columns configuration
  columns: any[] = [
    { field: 'interchangeSenderId', header: 'ISA Sender', sortable: true, width: '150px' },
    { field: 'interchangeControlNumber', header: 'ISA Ctrl #', sortable: true, width: '150px' },
    { field: 'id', header: 'Msg. ID', sortable: true, width: '100px' },
    {
      field: 'transactionSet',
      header: 'Related Messages',
      sortable: true,
      width: '100px',
      style: { 'min-width': '350px' },
    },
    { field: 'applicationStatus', header: 'Status', sortable: true, width: '100px' },
    { field: 'messageNetwork', header: 'Network', sortable: true, width: '100px' },
    { field: 'messageDateTime', header: 'Received', sortable: true, width: '100px' },
  ];

  globalFilterFields: string[] = [
    'interchangeSenderId',
    this.selectedCtrlNmbrField,
    'id',
    'transactionSet',
    'applicationStatus',
    'messageNetwork',
    'messageDateTime',
  ];

  // UI configuration
  inboundOutboundSelectedState: MessageDirections = MessageDirections.INBOUND;
  stateOptions: { label: string; value: MessageDirections }[] = [
    { label: 'Inbound (1000)', value: MessageDirections.INBOUND },
    { label: 'Outbound (0)', value: MessageDirections.OUTBOUND },
  ];
  contextMenuItems: MenuItem[] = SHARED_ACTIONS.map(item => ({
    ...item,
    command: () => {
      const result = item.command ? item.command() : { action: '', environment: undefined };
      if (result.action) {
        this.handleAction(result.action, result.environment);
      }
    },
    items: item.items?.map(subItem => ({
      ...subItem,
      command: () => {
        const result = subItem.command ? subItem.command() : { action: '', environment: undefined };
        if (result.action) {
          this.handleAction(result.action, result.environment);
        }
      },
    })),
  }));

  // Subscriptions
  private subscriptions = new Subscription();

  constructor(
    private route: ActivatedRoute,
    private searchService: SearchService,
    private filterService: FilterService,
    private store: Store,
    private dialogService: DialogService,
    private messageService: MessageService,
    private actionHandlerService: MessageActionHandlerService,
    private ediMessageService: EdiMessageService,
    private location: Location,
  ) {}

  ngOnInit(): void {
    this.setupRouteSubscriptions();
    this.registerCustomFilters();
    this.handleDeepLinking();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
    if (this.dialogRef) {
      this.dialogRef.close();
    }
  }

  // Handle filter field change
  onFilterFieldChange(): void {
    // Update the control number column (index 1 in columns array)
    this.columns[1] =
      this.selectedCtrlNmbrField === 'interchangeControlNumber'
        ? {
            field: 'interchangeControlNumber',
            header: 'ISA Ctrl #',
            sortable: true,
            width: '150px',
          }
        : {
            field: 'gsControlNumber',
            header: 'GS Ctrl #',
            sortable: true,
            width: '150px',
          };

    // Update globalFilterFields
    this.globalFilterFields = [
      'interchangeSenderId',
      this.selectedCtrlNmbrField,
      'id',
      'transactionSet',
      'applicationStatus',
      'messageNetwork',
      'messageDateTime',
    ];

    // Reset table to apply new filters and column configuration
    this.table.reset();
  }

  // Compute column styles
  getColumnStyle(col: any): { [key: string]: string } {
    return {
      width: col.width,
      ...(col.style || {}),
    };
  }

  // Setup Methods
  private setupRouteSubscriptions(): void {
    this.subscriptions.add(
      this.route.queryParams.subscribe(params => {
        this.error = null;
        this.searchParams = this.searchService.parseSearchParams(params);
        if (Object.keys(params).length > 0) {
          this.store.dispatch(SearchActions.saveSearchParams({ payload: params }));
        }
        this.executeSearch();
      }),
    );
  }

  private registerCustomFilters(): void {
    this.filterService.register(
      'customTransactionSetFilter',
      (value: any[], filter: string): boolean => {
        if (!filter) return true;
        if (!value) return false;
        const filterLower = filter.toLowerCase();
        return value.some(
          (transaction: Transaction) =>
            transaction.name?.toLowerCase().includes(filterLower) ||
            transaction.description?.toLowerCase().includes(filterLower),
        );
      },
    );
  }

  private handleDeepLinking(): void {
    this.subscriptions.add(
      this.route.paramMap.subscribe(params => {
        this.messageDetailId = params.get('id');
        if (this.messageDetailId) {
          this.loadMessageDetails(this.messageDetailId);
        }
      }),
    );
  }

  // Data Loading Methods
  private loadMessageDetails(id: string): void {
    this.isLoading = true;
    this.ediMessageService
      .getMessageById(id)
      .pipe(
        catchError(err => {
          this.error = err.message || 'An error occurred while retrieving the message';
          return of(null);
        }),
        finalize(() => {
          this.isLoading = false;
        }),
      )
      .subscribe(response => {
        if (response) {
          this.messageDetails = response;
          this.showRelatedModal(response);
        }
      });
  }

  executeSearch(): void {
    this.isLoading = true;
    this.searchService
      .search<Message>(this.searchParams)
      .pipe(
        catchError(err => {
          this.error = err.message || 'An error occurred while searching';
          return of(null);
        }),
        finalize(() => {
          this.isLoading = false;
        }),
      )
      .subscribe(response => {
        if (response) {
          this.messages = response.results;
          this.totalCount = response.totalCount;
          this.currentPage = response.page;
          this.pageSize = response.pageSize;
          this.isLastPage = response.isLastPage;
          this.updateFilterOptions();
        }
      });
  }

  // UI Interaction Methods
  applyDateFilter(event: Date | null): void {
    if (!this.table) return;
    this.dateFilterValue = event;
    this.table.filter(event, 'messageDateTime', 'customDateFilter');
  }

  showRelatedModal(message: Message, transaction?: Transaction): void {
    if (this.dialogRef) return;

    transaction = transaction || message.transactionSet[0];

    this.dialogRef = this.dialogService.open(DialogRelatedComponent, {
      header: `EDI: ${transaction.name} (${this.inboundOutboundSelectedState})`,
      width: '80%',
      data: { message, transaction },
      modal: true,
    });

    this.dialogRef.onClose.subscribe(() => {
      this.dialogRef = null;
      this.updateUrl('/search-results');
    });

    this.updateUrl(`/search-results/${message.id}`);
  }

  handleAction(action: string, environment?: string): void {
    const messages = this.selectedContextMessage
      ? [this.selectedContextMessage]
      : this.selectedMessages;
    const messageIds = messages.map(m => m.id);

    this.actionHandlerService.executeAction(action, messageIds).subscribe({
      next: response => {
        this.messageService.add({
          severity: 'success',
          summary: response.summary,
          detail: response.detail,
          life: 3000,
        });
      },
      error: err => {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: err.message,
          life: 3000,
        });
      },
    });
  }

  onToolbarAction(event: { action: string; environment?: string }): void {
    this.handleAction(event.action, event.environment);
  }

  // Utility Methods
  private updateFilterOptions(): void {
    this.networkOptions = [...new Set(this.messages.map(m => m.messageNetwork))].map(value => ({
      label: value,
      value,
    }));
  }

  private updateUrl(newPath: string): void {
    const queryParams = this.route.snapshot.queryParams;
    const queryString = Object.keys(queryParams)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(queryParams[key])}`)
      .join('&');
    const newUrl = queryString ? `${newPath}?${queryString}` : newPath;
    this.location.replaceState(newUrl);
  }

  getSeverity(status: string): string {
    switch (status) {
      case 'EDI_Rejected':
        return 'danger';
      case 'App_Record':
        return 'success';
      case 'Partner Map':
        return 'info';
      default:
        return 'warning';
    }
  }

  getAppRecordSeverity(message: Message): string {
    return message.sourceApplication ? 'success' : 'secondary';
  }

  // Logging for buttons
  logIt(event: string): void {
    console.log(event);
  }
}
