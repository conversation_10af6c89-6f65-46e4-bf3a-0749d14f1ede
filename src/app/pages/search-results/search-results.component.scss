:host {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.search-results-container {
  margin: 0 auto;
  font-family: Arial, sans-serif;
  padding-bottom: 1rem;
  display: flex;
  flex-grow: 1;
}

p-table {
  font-size: 14px;
  font-weight: 400;

  ::ng-deep .p-datatable-thead {
    border: 1px solid black;
  }

  ::ng-deep .p-datatable-thead > tr > th {
    background: var(--p-neutral-50);
    font-weight: 700;
    border: 0;

    &.p-datatable-column-sorted {
      background: var(--p-blue-50);
    }
  }

  ::ng-deep .p-button {
    font-size: 14px;
    padding: 0;
  }

  ::ng-deep p-columnfilterformelement,
  ::ng-deep p-columnfilterformelement input {
    width: 100%;
  }

  .related-messages-wrapper {
    display: flex;
    margin: 0 auto;
    padding: 0.5rem 0;

    .pi {
      margin-right: 0.5rem;
    }
  }

  .related-messages {
    display: flex;
    margin-right: 1rem;

    > div:not(:first-child):not(:last-child) {
      border-left: 1px solid #ccc;
      padding-left: 0.5rem;
      display: flex;
      flex-direction: row;
    }

    .related-message {
      display: inline-block;
      white-space: nowrap;

      > div {
        display: inline-flex;
        align-items: center;
        margin-right: 0.5rem;
      }

      p-badge {
        margin-right: 0.25rem;
      }
    }
  }

  .app-record-cell {
    min-width: 90px;

    .p-badge {
      margin-right: 0.25rem;
    }
  }

  .grow {
    width: 100%;
  }

  .fixed {
    width: 150px;
  }
}

.global-table-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .table-search-row {
    display: flex;
    width: 100%;
    justify-content: flex-end;

    .ctrl-number {
      margin-right: 1rem;
    }
  }
}

.flex-outside {
  flex: 0 1 450px;
}

.no-results {
  display: grid;
  place-items: center;

  .no-results-layout {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: left;
    padding: 2rem;
  }

  img {
    margin-bottom: 70px;
  }

  h2 {
    margin-bottom: 1rem;
  }
}
